package com.cheche365.bc.sharding;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import com.cheche365.bc.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@Slf4j
public class OldTableSwitch {

    private static final AtomicBoolean USE_OLD_TABLE = new AtomicBoolean(true);

    public static final String CACHE_KEY = "bc:oldTableSwitch";

    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(ThreadFactoryBuilder
        .create()
        .setNamePrefix("OldTableSwitch-scheduler-")
        .setDaemon(true)
        .setUncaughtExceptionHandler((t, e) -> log.error("Failed to execute OldTableSwitch scheduler task", e))
        .build());

    static {
//        init();
    }

    private static void init() {
        boolean value = getOldTableSwitch();
        USE_OLD_TABLE.set(true);
        initScheduler();

    }

    private static void initScheduler() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(1, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.error("Failed to shutdown scheduler", e);
            }
            log.info("OldTableSwitch scheduler stopped");
        }));

        scheduler.scheduleAtFixedRate(() -> {
            try {
                boolean newValue = getOldTableSwitch();
                if (USE_OLD_TABLE.get() != newValue) {
                    USE_OLD_TABLE.set(newValue);
                    log.info("旧表标识已更新为:{}", newValue);
                }
            } catch (Throwable e) {
                log.error("Failed to check old table switch", e);
            }
        }, 0, 10, TimeUnit.SECONDS);
    }

    private static boolean getOldTableSwitch() {
        String value = "";
        try {
            value = RedisUtil.get(CACHE_KEY);
        } catch (Exception e) {
            log.error("获取旧表标识失败", e);
        }
        if (StrUtil.isBlank(value)) {
            if (RedisUtil.setnx(CACHE_KEY, "true")) {
                value = "true";
            } else {
                try {
                    TimeUnit.MILLISECONDS.sleep(100);
                } catch (InterruptedException e) {
                    log.error("设置旧表标识失败", e);
                }
                value = RedisUtil.get(CACHE_KEY);
            }
        }
        return value.equals("true");
    }


    /**
     * 获取当前是否使用旧表的标识
     *
     * @return true表示使用旧表，false表示使用分表
     */
    public static boolean isUseOldTable() {
        return USE_OLD_TABLE.get();
    }

    public static void setUseOldTable(boolean useOldTable) {
        USE_OLD_TABLE.set(useOldTable);
        RedisUtil.set(CACHE_KEY, useOldTable + "");
    }

}
