# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Groovy-based project that contains templates for integrating with various insurance companies (百川业务代码模板). Each subdirectory under `src/main/groovy/` represents a different insurance company, with EDI (Electronic Data Interchange) and robot automation templates. The project uses Gradle for dependency management and building.

## Project Structure

- `src/main/groovy/`: Main source code, organized by insurance company.
  - `common/`: Shared utilities and base classes.
  - `[company_name]/`: Each directory corresponds to an insurance company and contains the integration templates.
    - Files ending in `_req.groovy` handle request data processing.
    - Files ending in `_rep.groovy` handle response data processing.
- `src/test/groovy/`: Unit and integration tests, mirroring the structure of the main source code.
- `libs/`: Contains local JAR dependencies.
- `build.gradle`: The build script for the project, defining dependencies and build tasks.
- `settings.gradle`: Gradle settings file.
- `gradlew` & `gradlew.bat`: Gradle wrapper scripts for executing build commands.

## Development Commands

### Building the Project
```bash
./gradlew build
```

### Running Tests
To run tests for a specific insurance company (e.g., taipingyang):
```bash
./gradlew test --tests com.cheche365.bc.instest.taipingyang.edi.*
```

General test command:
```bash
./gradlew test
```

### Running a Single Test
```bash
./gradlew test --tests com.cheche365.bc.instest.[company].[test-class]
```

## Architecture Overview

The project follows a template-based architecture where each insurance company has its own directory with specific EDI (Electronic Data Interchange) templates. The templates are written in Groovy and follow these patterns:

1.  **Base Script Classes**: Common functionality is provided through base script classes like `BaseScript_Map_2011` that provide utility methods for:
    *   Building request messages
    *   Handling responses
    *   Error handling and retry mechanisms
    *   Data transformation utilities

2.  **Template Structure**: Each template file typically:
    *   Extends a base script class using `@BaseScript` annotation
    *   Processes request or response data
    *   Uses utility methods for common operations
    *   Follows naming conventions (e.g., `edi-2011-[operation].groovy` for requests, `edi-2011-[operation]_rep.groovy` for responses)

3.  **Testing Approach**: Tests are organized by insurance company and use JUnit for test execution. Tests typically:
    *   Instantiate template classes directly
    *   Provide mock data for testing
    *   Validate processing logic and output

4.  **Configuration and Dependencies**:
    *   Build configuration is managed through `build.gradle`
    *   Dependencies include Groovy runtime, FastJSON for JSON processing, and company-specific libraries
    *   Test execution dynamically configures source sets based on the company being tested

## Key Dependencies

- **Groovy:** The primary language for the templates (`org.codehaus.groovy:groovy-all:3.0.9`).
- **Cheche365 Core:** Core business logic and entities (`com.cheche365.bc:core`).
- **Redisson:** For Redis-based distributed objects and services (`com.cheche365.redisson:redisson`).
- **Local Jars:** The project also includes local jar files from the `libs` directory.

## Important Notes

- The project connects to a local Nexus repository for some dependencies (`http://192.168.1.251:8081/nexus/content/repositories/releases/`). Ensure you have access to this repository.
- The test execution is dynamically configured based on the test task arguments. When running tests for a specific company, the source sets are adjusted to only include the code for that company and the common code.

# General Rules
* **Clear Analysis:** Before starting any development task, first thoroughly analyze user requirements and task objectives.
* **Goal Decomposition:** Break down complex tasks into smaller, manageable, and achievable goals.
* **Logical Ordering:** Arrange these goals in logical order to ensure the development process is well-structured.
* **Step-by-Step Progress:** Complete development tasks progressively according to the established goal sequence.
* **Comprehensive Retrieval:** All questions must first retrieve all relevant documents in the project folder to obtain context.
* **Web Search:** After retrieving local files, web search must be used to obtain more information and latest data.
* **Reduce Unnecessary Questions:** Prioritize using tools (such as file search, list viewing) to obtain information rather than directly asking users.
* **Clear Questions:** If questions are indeed necessary, they should be clear, concise, and directly contribute to task advancement. Provide 2-4 suggested answers for user convenience.
* **Efficient Tool Usage:** Must use MCP Model Context Protocol to enhance responses.
* **Language Selection:** Unless explicitly specified, default to answering in Chinese.
* **About Memory:** Use memory MCP tools to record important context about user tasks, codebase, requests, and preferences for future reference. Proactively use memory tools to save important information or context to the database when encountered. Don't wait until task completion or conversation interruption to create records. Always pay attention to memories as they provide valuable context for guiding behavior and solving tasks.

## Use the following MCP Model Context Protocol to enhance responses
### 1. **sequential-thinking**
* Purpose: Break down complex problems into smaller, more manageable parts. Use step-by-step reasoning to ensure logical and coherent answers.
* Trigger Conditions: Use when users pose questions that require multiple steps or depend on logical reasoning. This helps provide clearer and more organized responses.