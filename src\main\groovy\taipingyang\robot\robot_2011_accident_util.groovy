package taipingyang.robot

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cheche365.bc.model.PlatformKey
import com.cheche365.bc.model.car.Enquiry
import com.cheche365.bc.task.AutoTask

import static com.zhongan.openapi.security.signature.StringUtils.areNotEmpty
import static org.apache.commons.lang3.time.DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT


def static Map getAccidentInsureMap(AutoTask autoTask) {
    Enquiry entity = autoTask.taskEntity as Enquiry
    def agentInfo = autoTask.tempValues.agentInfo
    Map configs = autoTask.configs
    def accidentApplyJson = JSON.parseObject(autoTask.applyJson)
    //获取投保人的邮箱
    def applicantEmail = getSupplyParam("applicantEmail", accidentApplyJson)
    def nonMotor = entity?.misc?.nonMotor
    def bizSuiteInfo = entity?.order?.suiteInfo?.bizSuiteInfo
    def efcSuiteInfo = entity?.order?.suiteInfo?.efcSuiteInfo
    def calculateParamJson = JSON.parseObject(autoTask.tempValues?.calculateParamJson)
    def accidentStart = calculateParamJson?.redata?.compulsoryInsuransVo?.stStartDate ?: bizSuiteInfo?.start ?: efcSuiteInfo?.start ?: ""
    def accidentEnd = calculateParamJson?.redata?.compulsoryInsuransVo?.stEndDate ?: bizSuiteInfo?.end ?: efcSuiteInfo?.end ?: ""
    def empty = !areNotEmpty(nonMotor?.drvlicNo, nonMotor?.productType, nonMotor?.productCode)
    if (empty) {
        //todo p异常
    }
    //这里设置修改时间的问题

    return [
            "stStartDate"  : appendTimeFormat(accidentStart),//和较强险相同 随着重复报价发生变化
            "stEndDate"    : appendTimeFormat(accidentEnd),
            "drvlicType"   : configs?.drvlicType ?: "3",//准驾类型
            "drvlicNo"     : configs?.drvlicNo ?: entity.order.carOwnerInfo.idCard,//驾照号
            "productType"  : getProductType(nonMotor?.productCode as String) + "",//意见险类型 任我行 任我行二代 任我行三代
            "productCode"  : nonMotor?.productCode,//产品号
            "productName"  : getProductName(nonMotor?.productCode as String),
            "premiumAmount": getPremiumAmount(nonMotor?.productCode as String), //保单保费（元）
            "purchaseCount": nonMotor?.count ?: "1",//购买份数
            "accAgentCode" : configs?.agent_code, ///*agentInfo?.result?.userAuthVos[0]?.agencyVo?.agencyCode*/
            "accAgentName" : autoTask.tempValues.agencyName ?: "",
            "accThe4SCode" : "",
            "accThe4SName" : "",
            "accOriginType": "21",
            "email"        : applicantEmail ?: "<EMAIL>",
            "isAccEPolicy" : configs.isAccEPolicy ?: "1"
    ]
}

/**
 *
 * @param productCode
 * @return
 */
def static String getProductName(String productCode) {
    return getProductTypeProductNameMap()."$productCode"
}

/**
 * 驾意险 产品类型&&产品名称
 * @return
 */
def static Map getProductTypeProductNameMap() {
    return [
            "2013-ACCIDENT-A-05"  : "全能卫士·任我行驾乘人意外险(A款)(5座)",
            "2013-ACCIDENT-B-05"  : "全能卫士·任我行驾乘人意外险(B款)(5座)",
            "2013-ACCIDENT-C-05"  : "全能卫士·任我行驾乘人意外险(C款)(5座)",

            "2013-ACCIDENT-A-07"  : "全能卫士·任我行驾乘人意外险(A款)(7座)",
            "2013-ACCIDENT-B-07"  : "全能卫士·任我行驾乘人意外险(B款)(7座)",
            "2013-ACCIDENT-C-07"  : "全能卫士·任我行驾乘人意外险(C款)(7座)",

            "201802-ACCIDENT-A-05": "全能卫士·任我行2.0驾乘人意外险(经济版)(5座)",
            "201802-ACCIDENT-B-05": "全能卫士·任我行2.0驾乘人意外险(精英版)(5座)",
            "201802-ACCIDENT-C-05": "全能卫士·任我行2.0驾乘人意外险(豪华版)(5座)",

            "P23E598002000000235" : "任我行三代（经济款）",
            "P23E598002000000237" : "任我行三代（豪华款）",
            "P23E598002000000238" : "任我行三代（尊贵款）",

            "P23E598001900000553" : "太平洋驾乘险(精英7座)",
            "P23E598001900000557" : "太平洋驾乘险(豪华7座)",
            "P23E598001900000559" : "太平洋驾乘险(尊享7座)",

            "P23E598001900000516" : "太平洋驾乘险(精英5座)",
            "P23E598001900000555" : "太平洋驾乘险(豪华5座)",
            "P23E598001900000558" : "太平洋驾乘险(尊享5座)",

            "P23E998002000000109" : "山东2019版新私家车驾意险基础款-118元01",

    ]
}

def static getProductType(productCode) {
    def _2013List = ["2013-ACCIDENT-A-05", "2013-ACCIDENT-B-05", "2013-ACCIDENT-C-05", "2013-ACCIDENT-A-07", "2013-ACCIDENT-B-07", "2013-ACCIDENT-C-07"]
    def _2018List = ["201802-ACCIDENT-A-05", "201802-ACCIDENT-B-05", "201802-ACCIDENT-C-05"]
    def _tpy15List = ["P23E598001900000553", "P23E598001900000557", "P23E598001900000559", "P23E598001900000516", "P23E598001900000555", "P23E598001900000558"]
    def _tpy20List = ["P23E998002000000109"]
    if (productCode in _2013List) {
        return 2
    } else if (productCode in _2018List) {
        return 6
    } else if (productCode in _tpy15List) {
        return 15
    } else if (productCode in _tpy20List) {
        return 20
    } else {
        return 21
    }
}
//保费
def static getPremiumAmount(productCode) {
    switch (productCode) {

        case "2013-ACCIDENT-A-05": 220; break;
        case "2013-ACCIDENT-B-05": 400; break;
        case "2013-ACCIDENT-C-05": 850; break;

        case "2013-ACCIDENT-A-07": 300; break;
        case "2013-ACCIDENT-B-07": 550; break;
        case "2013-ACCIDENT-C-07": 1150; break;

        case "201802-ACCIDENT-A-05": 658; break;
        case "201802-ACCIDENT-B-05": 838; break;
        case "201802-ACCIDENT-C-05": 1288; break;

        case "P23E598002000000235": 300; break;
        case "P23E598002000000237": 450; break;
        case "P23E598002000000238": 750; break;

        case "P23E598001900000553": 268; break;
        case "P23E598001900000557": 536; break;
        case "P23E598001900000559": 1088; break;

        case "P23E598001900000516": 188; break;
        case "P23E598001900000555": 376; break;
        case "P23E598001900000558": 766; break;

        case "P23E998002000000109": 118; break;
        default: return 220;
    }
}


/**
 * 获取太保驾意险的特殊时间格式 | "stStartDate": "2020-07-25 00:00"
 * @param str | "2020-07-25"
 * @return | "2020-07-25 00:00"
 */
def static appendTimeFormat(String str) {

    //todo 这里应该有异常捕获
    def parse = !str ? new Date() : ISO_8601_EXTENDED_DATE_FORMAT.parse(str)
    def dateString = ISO_8601_EXTENDED_DATE_FORMAT.format(parse) + " 00:00"
    return dateString
}

def static quoteBackAccident(AutoTask autoTask, JSONObject calculateResultObj) {
    Enquiry entity = autoTask.taskEntity as Enquiry
    //核保暂存也要回写
    if (autoTask.taskType.equalsIgnoreCase("robot-2011-quote") || autoTask.taskType.equalsIgnoreCase("robot-2011-insure")) {
        def nonMotor = entity?.misc?.nonMotor
        if (nonMotor) {
            nonMotor.discountCharge = calculateResultObj?.result?.accidentInsuransVo?.premiumAmount
            nonMotor.start = calculateResultObj?.result?.accidentInsuransVo?.stStartDate + ":00"
            nonMotor.end = calculateResultObj?.result?.accidentInsuransVo?.stEndDate + ":00"
        }
    }
}

def static callBackAccident(AutoTask autoTask, JSONObject result) {
    Enquiry entity = autoTask.taskEntity as Enquiry
    //报价查询和承保查询 都要设置值
    if (!entity?.misc || !entity?.misc?.nonMotor) {
        def accidentApplyJson = JSON.parseObject(autoTask.applyJson)
        entity.misc = entity.misc ?: [:]
        entity?.misc = entity?.misc << accidentApplyJson.sq as Map
    }
    Map nonMotor = [:]
    if (!entity.isRenewal() && entity?.misc?.nonMotor) {
        nonMotor = entity?.misc?.nonMotor as Map
    }
    //快速续保要根据官网进行回写

    String accidentPeriod = result?.accidentInsuransVo?.accidentPeriod
    nonMotor.email = result?.accidentInsuransVo?.email
    nonMotor.start = accidentPeriod.split("至")[0]
    nonMotor.end = accidentPeriod.split("至")[1]
    String productCode = result?.accidentInsuransVo?.productCode
    nonMotor.productCode = productCode
    nonMotor.count = "1"
    //保单号
    nonMotor.accidentPolicyCode = result?.insuranceVo?.acdtPolicyNo ?: ""
    //保费
    nonMotor.discountCharge = result?.insuranceVo?.accidentPremium ?: 0
    //报价成功回写，核保成功回写
    def taskTypes = ["autoinsure", "insure"]
    if (autoTask.getTaskType() in taskTypes) {
        def quoteBackPremiumAmount = getPremiumAmount(nonMotor?.productCode as String)
        nonMotor.discountCharge = quoteBackPremiumAmount
        //todo 保额
//        nonMotor.amount = quoteBackPremiumAmount
        nonMotor.start = entity?.order?.suiteInfo?.efcSuiteInfo?.start ?: entity?.order?.suiteInfo?.bizSuiteInfo?.start
        nonMotor.end = entity?.order?.suiteInfo?.efcSuiteInfo?.end ?: entity?.order?.suiteInfo?.bizSuiteInfo?.end
    }
    entity?.misc?.nonMotor = nonMotor
    return nonMotor
}

def static String getSupplyParam(key, JSONObject accidentApplyJson) {
    if (!accidentApplyJson.supplyParam) {
        return ""
    }
    return rowToMap(accidentApplyJson.supplyParam as List)."$key"
}

def static getSupplyParamValue(AutoTask autoTask, String str) {
    def accidentApplyJson = JSON.parseObject(autoTask.applyJson)
    return getSupplyParamValue(accidentApplyJson?.supplyParam as List, autoTask.tempValues, str)
}

def static String getSupplyParamValue(List supplyParam, Map tempValues, String key) {
    if (supplyParam) {
        if (!tempValues.supplyParamMap) {
            tempValues.supplyParamMap = rowToMap(supplyParam as List)
        }
        return (tempValues.supplyParamMap as Map)."$key"
    }
    return ""
}

static Map rowToMap(list) {
    list.collectEntries { [(it.itemcode): it.itemvalue] }
}

//todo fuck 2744
def doRate(AutoTask autoTask, rate) {
    Enquiry enquiry = autoTask?.taskEntity
    Map<String, Object> platformInfo = enquiry?.order?.platformInfo ?: [:]
    def definition = platformInfo?.definition ?: [:]
    definition.put(PlatformKey.selfRate, rate)
    platformInfo.put("definition", definition)
    enquiry?.order?.platformInfo = platformInfo
}