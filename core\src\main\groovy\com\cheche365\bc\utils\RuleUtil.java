package com.cheche365.bc.utils;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.constants.PlatformConstants;
import com.cheche365.bc.model.PlatformKey;
import com.cheche365.bc.model.RuleInfoKey;
import com.cheche365.bc.model.car.*;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.DateCalcUtil;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.cheche365.bc.model.PlatformKey.*;


/**
 * Created by Co on 2017/3/2.
 */
@Slf4j
public class RuleUtil {

    /**
     * 获取请求信息中 ruleId,ruleUrl(数据仅为服务器ip)
     * 拼接平台返回信息请求规则折扣数据
     *
     * @param autoTask,companyCode(保险公司出单系统账号String),agentOrgCode(代理点编号String),affiliationCode(业务归属代码String)
     */
    private static JSONObject initParams(AutoTask autoTask, String companyCode, String agentOrgCode, String affiliationCode) throws Exception {
        //http接口请求参数
        JSONObject httpParams = new JSONObject();
        try {
            //单方任务
            Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
            //订单信息
            Order orderInfo = enquiry.getOrder();
            //车辆信息
            CarInfo carInfo = orderInfo.getCarInfo();
            //规则id
            String ruleId = checkParam(autoTask.getRuleId());

            //投保地市
            String insArea = "";

            String province = checkParam(orderInfo.getInsureArea().getProvince());

            String city = orderInfo.getInsureArea().getCity();
            //投保地市为空，用投保省份代替！
            insArea = StringUtil.isEmpty(city) ? province : city;
            //车系名称   待添加
            String familyName = checkParam(orderInfo.getCarInfo().getFamilyName());

            //玻璃类型
            String glassType = orderInfo.getCarInfo().getGlassType() == 0 ? "1" : "2";
            //规则日志编号
            String key1;
            if (checkParam(autoTask.getRuleUrl()).contains("/executor/")) {
                key1 = checkParam(autoTask.getCompanyId()).split("-")[0];
            } else {
                key1 = autoTask.getCompanyId();
            }
            String logId = checkParam(String.format("%s_%s", autoTask.getTraceKey(), key1));

            //获取平台信息从map中获取各公司开发自己存入的平台信息
            Map<String, Object> platform = orderInfo.getPlatformInfo();

            String bizScore = checkValue(platform, PlatformKey.bizScore);//业务评分
            String trafficScore = checkValue(platform, PlatformKey.TRAFFIC_SCORE);//业务评分
            String serviceCode = checkValue(platform, PlatformKey.SERVICE_CODE);//业务评分
            String taxDerateType = checkValue(platform, RuleInfoKey.RULE_ITEM_TAX_DERATE_TYPE);//纳税类型
            //断保天数
            String brokenInsureDays = checkValue(platform, RuleInfoKey.RULE_BROKEN_INSURE_DAYS);
            String lastInsureCo = checkValue(platform, PlatformKey.application_lastInsureCo);//上年承保公司
            // if (StringUtils.isBlank(lastInsureCo)) {
            //     lastInsureCo = getLastInsureCo(platform);
            // }
            lastInsureCo = PlatformConstants.getInsCode(lastInsureCo);
            if (StringUtils.isNotBlank(orderInfo.getLastYearInfo().getLastComId())) {
                lastInsureCo = orderInfo.getLastYearInfo().getLastComId();
            }
            String lastClaimSum = checkValue(platform, PlatformKey.lastClaimSum);//理赔金额
            String firstInsureType = checkValue(platform, PlatformKey.firstInsureType);//投保类型
            firstInsureType = changeFirstType(firstInsureType);//数据转换
            String loyaltyReasons = checkValue(platform, PlatformKey.loyaltyReasons);//无赔款折扣不浮动原因
            loyaltyReasons = changeReasons(loyaltyReasons);
            String rateCarPrice = checkValue(platform, PlatformKey.rateCarPrice);//实际价值
            String price = checkValue(platform, PlatformKey.price);//新车购置价
            String taxPrice = checkValue(platform, PlatformKey.taxPrice);//新车购置价
            String carAge = checkValue(platform, PlatformKey.carAge);//车龄

            String useNatureCode = checkParam(String.valueOf(carInfo.getUseProps()));//车辆使用性质
            useNatureCode = changeUseProps(useNatureCode);
            String userType = checkParam(String.valueOf(carInfo.getCarUserType()));//所属性质
            userType = changeBelongsNatureCode(userType);
            String brandName = checkParam(carInfo.getCarBrandName());//车型品牌
            String modelName = checkParam(carInfo.getCarModelName());//车型名称
            String licenseNo = checkParam(carInfo.getPlateNum());//车牌号码
            String exhaustScale = checkParam(String.valueOf(carInfo.getDisplacement()));//排气量
            String seatCount = checkParam(String.valueOf(carInfo.getSeatCnt()));//座位数
            String tonCount = checkParam(String.valueOf(carInfo.getModelLoad()));//载重量
            double ton = StringUtil.isNoEmpty(tonCount) ? Double.parseDouble(tonCount) : 0;
            tonCount = String.valueOf(ton / 1000);
            String enrollDate = checkParam(DateCalcUtil.getFormatDate(carInfo.getFirstRegDate(), "yyyy-M-d"));//车辆初登日期
            JSONArray bizPolicies = JSONArray.parseArray(checkValue(platform, PlatformKey.bizPolicies));
            String policyStartTime = null;
            if (null != bizPolicies && bizPolicies.size() > 0) {
                policyStartTime = bizPolicies.getJSONObject(0).getString(PlatformKey.policyStartTime);
            }
            if (StringUtils.isBlank(policyStartTime)) {
                BaseSuiteInfo baseSuiteInfo = enquiry.getOrder().getSuiteInfo();
                policyStartTime = Objects.nonNull(baseSuiteInfo.getBizSuiteInfo()) ? baseSuiteInfo.getBizSuiteInfo().getStart() : baseSuiteInfo.getEfcSuiteInfo().getStart();
            }
            //bizPolicies 获取第一个险种信息 上年起保时间，如果为空做 T+1处理
            BaseSuiteInfo baseSuiteInfo = enquiry.getOrder().getSuiteInfo();
            if (baseSuiteInfo.getBizSuiteInfo() != null) {
                policyStartTime = baseSuiteInfo.getBizSuiteInfo().getStart();
            }
            if (StringUtils.isEmpty(policyStartTime)) {
                policyStartTime = baseSuiteInfo.getEfcSuiteInfo().getStart();
            }
            policyStartTime = policyStartTime.substring(0, 10);
            String policyEndTime = LocalDate.parse(policyStartTime).plusYears(1).format(DateTimeFormatter.ofPattern("yyyy-M-d"));
            policyStartTime = LocalDate.parse(policyStartTime).format(DateTimeFormatter.ofPattern("yyyy-M-d"));
            String bwCommercialClaimTimes = checkValue(platform, PlatformKey.bwCommercialClaimTimes);//上年商业理赔次数
            String bwCompulsoryClaimTimes = checkValue(platform, PlatformKey.bwCompulsoryClaimTimes);//上年交强理赔次数
            bwCommercialClaimTimes = commercialClaimTimes(bwCommercialClaimTimes);//数据转换
            bwCompulsoryClaimTimes = compulsoryClaimTimes(bwCompulsoryClaimTimes);//数据转换
            String isTransfer = checkParam(String.valueOf(carInfo.getIsTransfer()));//是否过户车
            String noClaimDiscountCoefficient = checkValue(platform, PlatformKey.noClaimDiscountCoefficient);//无赔款优待系数
            String selfRate = checkValue(platform, PlatformKey.selfRate);//自主定价系数
            String bizContinuityInsureYears = checkValue(platform, PlatformKey.bizContinuityInsureYears);//商业险连续承保年数
            String vehicleOrigin = "0";//车辆产地类型
            String vin = carInfo.getVin();
            if (vin.length() >= 17 && (vin.startsWith("L") || vin.startsWith("l"))) {
                vehicleOrigin = "1";
            } else {
                vehicleOrigin = "2";
            }
            String insuredName = checkParam(orderInfo.getInsuredPersons().get(0).getName());//被保险人姓名
            String insuredCurrentAge = CommonUtils.getCurrentAge(orderInfo.getInsuredPersons().get(0).getBirthday());//被保险人年龄
            String appliName = checkParam(orderInfo.getInsurePerson().getName());//投保人姓名
            String applicationCurrentAge = CommonUtils.getCurrentAge(orderInfo.getInsurePerson().getBirthday());//投保人年龄
            String carOwner = checkParam(orderInfo.getCarOwnerInfo().getName());//车主姓名
            String identifyTypeIdv = String.valueOf(orderInfo.getInsuredPersons().get(0).getIdCardType());//被保险人证件类型
            String appliIdentifyTypeIdv = String.valueOf(orderInfo.getInsurePerson().getIdCardType());//投保人证件类型
            String ownerIdentifyType = String.valueOf(orderInfo.getCarOwnerInfo().getIdCardType());//车主证件类型
            String specialType = checkParam(carInfo.getSyvehicletypecode());//特种车类型
            specialType = changeSpecialType(specialType);
            //请求参数组装

            for (String key : platform.keySet()) {
                if (key.contains(".")) {
                    httpParams.put(key, platform.get(key));
                }
            }

            int insureType = 0;
            if (Objects.nonNull(orderInfo.getSuiteInfo().getBizSuiteInfo()))
                insureType += 1;
            if (Objects.nonNull(orderInfo.getSuiteInfo().getEfcSuiteInfo()))
                insureType += 2;
            httpParams.put(RuleInfoKey.INSURETYPE, String.valueOf(insureType));

            httpParams.put(RuleInfoKey.interface_name, "geniusRule");

            httpParams.put(RuleInfoKey.ruleItem_ruleID, ruleId);//规则ID(Long)

            httpParams.put(RuleInfoKey.ruleItem_ruleGroupId, ruleId);//规则ID(Long) (适配新规则引擎)

            httpParams.put(RuleInfoKey.api_log_enquiry_id, logId);//规则日志编号(String)

            httpParams.put(RuleInfoKey.application_insureCompanyCode, StringUtil.isNoEmpty(companyCode) ? companyCode : "");//保险公司出单系统账号(String)

            httpParams.put(RuleInfoKey.application_agentOrgCode, StringUtil.isNoEmpty(agentOrgCode) ? agentOrgCode : "");//代理点编号(String)

            httpParams.put(RuleInfoKey.application_deptCode, StringUtil.isNoEmpty(enquiry.getDeptCode()) ? enquiry.getDeptCode() : "");//出单网点编码(String)

            httpParams.put(RuleInfoKey.application_affiliationCode, StringUtil.isNoEmpty(affiliationCode) ? affiliationCode : "");//业务归属代码(String)

            httpParams.put(RuleInfoKey.application_lastCommercialClaimSum, lastClaimSum);//理赔金额(Double)

            httpParams.put(RuleInfoKey.car_specific_userType, userType);//所属性质(Integer)

            httpParams.put(RuleInfoKey.car_specific_useProps, useNatureCode);//车辆使用性质(Long)

            httpParams.put(RuleInfoKey.application_commercialClaimTimes, bwCommercialClaimTimes);//上年商业理赔次数(Integer)

            httpParams.put(RuleInfoKey.application_firstInsureType, firstInsureType);//投保类型(Integer)

            httpParams.put(RuleInfoKey.car_specific_depPrice, rateCarPrice);//实际价值(Double)

            httpParams.put(RuleInfoKey.car_specific_Price, price);//新车购置价(Double)
            httpParams.put(RuleInfoKey.car_specific_taxPrice, taxPrice);//新车购置价(Double 含税)


            httpParams.put(RuleInfoKey.car_specific_useMonth, carAge);//车龄(Integer)ps:规则那边另做计算

            httpParams.put(RuleInfoKey.car_model_brandName, brandName);//车型品牌(String)

            httpParams.put(RuleInfoKey.car_model_modelName, modelName);//车型名称(String)

            httpParams.put(RuleInfoKey.car_model_familyName, familyName);//车型系列名称(String)

            httpParams.put(RuleInfoKey.application_insureArea, insArea);//地区编号(Long)

            httpParams.put(RuleInfoKey.car_specific_license, licenseNo);//车牌号码(String)

            httpParams.put(RuleInfoKey.car_model_modelLoad, tonCount);//载重量(Double)

            httpParams.put(RuleInfoKey.car_model_seats, seatCount);//座位数(Integer)
            //断保天数
            httpParams.put(RuleInfoKey.RULE_BROKEN_INSURE_DAYS, brokenInsureDays);

            //交强险险种配置信息
            EfcSuiteInfo efcSuiteInfo = baseSuiteInfo.getEfcSuiteInfo();
            if (efcSuiteInfo != null) {
                httpParams.put(RuleInfoKey.insureItem_vehicleCompulsoryIns_isInsured, true);//投保交强险
            }
            //http://192.168.1.212/index.php?m=bug&f=view&bugID=20286
            //修改传给规则的商业险 险种字段。不再使用费改之前的险种字段。  去掉涉水险保额传送 修改三者 划痕 key
            //商业险险种配置信息
            if (baseSuiteInfo.getBizSuiteInfo() != null) {
                Map<String, SuiteDef> suiteDef = baseSuiteInfo.getBizSuiteInfo().getSuites();
                if (!suiteDef.isEmpty()) {
                    suiteDef.forEach((k, v) -> {
                        String suiteCode = v.getCode();
                        httpParams.put("insureItem." + CharSequenceUtil.lowerFirst(suiteCode) + ".isInsured", true);
                        switch (suiteCode) {
                            case "ThirdParty":
                                httpParams.put("insureItem.thirdParty.coverage", v.getAmount());
                                break;
                            case "Scratch":
                                httpParams.put("insureItem.scratch.coverage", v.getAmount());
                                break;
                        }
                    });
                }
            }

            httpParams.put(RuleInfoKey.car_model_displacement, exhaustScale);//排气量(Double)

            httpParams.put(RuleInfoKey.car_specific_glassType, glassType);//玻璃类型(Integer)

            httpParams.put(RuleInfoKey.car_specific_regDate, enrollDate);//车辆初登日期(Date)格式:yyyy-M-d

            httpParams.put(RuleInfoKey.application_commercialEffectiveDate, policyStartTime);//起保日期(Date)格式:yyyy-M-d

            httpParams.put(RuleInfoKey.application_commercialExpiryDate, policyEndTime);//商业险终止日期(Date)格式:yyyy-M-d

            httpParams.put(RuleInfoKey.application_compulsoryClaimTimes, bwCompulsoryClaimTimes);//上年交强理赔次数(Integer)

            httpParams.put(RuleInfoKey.car_specific_isTransferCar, isTransfer);//是否过户车(Boolean)

            httpParams.put(RuleInfoKey.car_model_vehicleOrigin, vehicleOrigin);//车辆产地类型(Integer)

            httpParams.put(RuleInfoKey.insured_name, insuredName);//被保险人姓名(String)

            httpParams.put(RuleInfoKey.insured_age, insuredCurrentAge);//被保险人年龄(String)

            httpParams.put(RuleInfoKey.proposer_name, appliName);//投保人姓名(String)

            httpParams.put(RuleInfoKey.proposer_age, applicationCurrentAge);//投保人年龄(String)

            httpParams.put(RuleInfoKey.car_owner_name, carOwner);//车主姓名(String)
            String insuredIDType = idCardType(Integer.parseInt(identifyTypeIdv));
            httpParams.put(RuleInfoKey.insured_certificate, insuredIDType);//被保险人证件类型(Integer)
            String insureIDType = idCardType(Integer.parseInt(appliIdentifyTypeIdv));
            httpParams.put(RuleInfoKey.proposer_certificate, insureIDType);//投保人证件类型(Integer)
            String ownerIDType = idCardType(Integer.parseInt(ownerIdentifyType));
            httpParams.put(RuleInfoKey.car_owner_certificate, ownerIDType);//车主证件类型(Integer)

            httpParams.put(RuleInfoKey.application_lastInsureCo, lastInsureCo);//上年承保公司(String)

            httpParams.put(RuleInfoKey.car_specific_specialVehicleType, specialType);//特种车类型(Long)

            httpParams.put(RuleInfoKey.ruleItem_noRenewalDiscountReason, loyaltyReasons);//无赔款折扣不浮动原因(Integer)

            httpParams.put(RuleInfoKey.ruleItem_noCompensationDiscount, noClaimDiscountCoefficient);//无赔款优待系数
            httpParams.put(RuleInfoKey.geniusItem_totalDiscount, selfRate);//自主定价系数
            httpParams.put(RuleInfoKey.ruleItem_bizContinuityInsureYears, bizContinuityInsureYears); //商业险连续承保年数

            if (StringUtil.isNoEmpty(bizScore)) {
                httpParams.put(RuleInfoKey.application_bizScore, bizScore);//业务评分(String)
            }
            if (StringUtil.isNoEmpty(trafficScore)) {
                httpParams.put(RuleInfoKey.TRAFFIC_SCORE, trafficScore);//业务评分(String)
            }
            if (StringUtil.isNoEmpty(serviceCode)) {
                httpParams.put(RuleInfoKey.SERVICE_CODE, serviceCode);//业务评分(String)
            }
            if (StringUtil.isNoEmpty(taxDerateType)) {
                httpParams.put(RuleInfoKey.RULE_ITEM_TAX_DERATE_TYPE, taxDerateType);//纳税类型(String)
            }

            //添加definition信息给规则
            Map<String, String> definitionMap = (Map<String, String>) platform.get("definition");
            if (definitionMap != null && definitionMap.size() > 0) {
                for (String key : definitionMap.keySet()) {
                    httpParams.put(key, definitionMap.get(key));
                }
            }

            Object ruleAttachedInfo = platform.get("ruleAttachedInfo");
            if (ruleAttachedInfo != null && ruleAttachedInfo instanceof Map) {
                httpParams.putAll((Map) ruleAttachedInfo);
            }
        } catch (Exception e) {
            log.error("规则调用参数组装出错：" + e.toString(), e);
            throw e;
        }
        return httpParams;
    }

    /**
     *需求3660，传规则证件类型要按需求转换
     * @param oldValue
     * @return
     */
    private static String idCardType(int oldValue) {
        //身份证:1,户口本:2,军官证/士兵证:3,护照:4,港澳回乡证:5,税务登记证:9,营业执照:10,香港身份证:11,驾照:12,台胞证:13,组织机构代码证:21,社会信用代码证:22,其它:99
        int newValue = 0;
        switch (oldValue){
            case 0 : newValue = 1; break;
            case 1 : newValue = 2; break;
            case 2 : newValue = 12; break;
            case 3 : newValue = 3; break;
            case 4 : newValue = 4; break;
            case 5 : newValue = 5; break;
            case 6 : newValue = 21; break;
            case 7 : newValue = 99; break;
            case 8 : newValue = 22; break;
            case 9 : newValue = 9; break;
            case 10 : newValue = 10; break;
            case 11 : newValue = 11; break;
            case 12 : newValue = 13; break;
        }
        return String.valueOf(newValue);
    }

    /**
     * platform.insCorpName
     * platform.insCorpCode
     * platform.bizClaims[0].insCorpCode
     * platform.bizPolicies[0].insCorpName
     *
     * @param platform
     * @return
     */
    private static String getLastInsureCo(Map<String, Object> platform) {
        String str = (String) (platform.get(insCorpName));
        if (StringUtils.isBlank(str)) {
            str = (String) platform.get("plateform_InsureCo");
        }
        if (StringUtils.isBlank(str)) {
            str = (String) platform.get(insCorpCode);
        }
        if (StringUtils.isBlank(str)) {
            str = doGetLastInsureCo(platform, bizClaims, insCorpCode);
        }
        if (StringUtils.isBlank(str)) {
            str = doGetLastInsureCo(platform, bizPolicies, insCorpName);
        }
        if (StringUtils.isBlank(str)) {
            Map task = (Map) platform.get("task");
            if (task != null) {
                str = (String) task.get("plateform.InsureCo");
            }
        }
        return str;
    }

    private static String doGetLastInsureCo(Map<String, Object> platform, String claims, String code) {
        List list = (List) platform.get(claims);
        Object str = null;
        if (list != null && list.size() > 0) {
            str = ((Map) list.get(0)).get(code);
        }
        return str == null ? "" : str.toString();
    }

    /**
     * @param autoTask
     * @param comCode   保险公司出单系统账号
     * @param agentCode 代理点编号
     * @param affCode   业务归属代码
     */
    public static void doRuleInfo(AutoTask autoTask, String comCode, String agentCode, String affCode) {
        try {
            if (StringUtil.isNoEmpty(autoTask.getRuleUrl())) {
                //请求地址
//                String httpUrl = "http://***************:8080/ruleHandler/geniusRule";
                String httpUrl;
                // 适配新规则引擎，新规则传完整url，旧规则只传域名或IP
                String ruleUrl = checkParam(autoTask.getRuleUrl());
                if (ruleUrl.contains("/executor/")) {
                    httpUrl = ruleUrl;
                } else {
                    httpUrl = "http://" + ruleUrl + ":8080/ruleHandler/geniusRule";
                }
                JSONObject httpParams = initParams(autoTask, comCode, agentCode, affCode);
                autoTask.getTempValues().put("ruleRequestInfo", httpParams);
                getRuleInfoForHTTP(autoTask, httpUrl, httpParams);
            } else {
                log.info("规则地址为空。。");
            }

        } catch (Exception e) {
            log.info("规则调用出错：" + e.getMessage(), e);
        }

    }

    /**
     * http接口获取规则
     *
     * @return
     * @throws Exception
     */
    private static JSONObject getRuleInfoForHTTP(AutoTask autoTask, String httpUrl, JSONObject httpParams) {
        String msg = "";
        JSONObject callBakMsg = null;
        try {
            String request = httpParams.toJSONString();
            //调用规则http接口
            log.info("调规则传入数据为:{}", request);
            autoTask.setConcatResultStr(String.format("调规则传入数据为:%s", request));
            msg = HttpSender.doPost(httpUrl, request);
            autoTask.setConcatResultStr(String.format("规则返回内容为:%s", msg));
            callBakMsg = JSONObject.parseObject(msg);
            // 适配新规则引擎
            if (callBakMsg.containsKey("code") && callBakMsg.getInteger("code") == 200) {
                callBakMsg = callBakMsg.getJSONObject("data");
            }
        } catch (Exception e) {
            log.info("规则返回结果，转换JSON对象失败：" + e.getMessage(), e);
            autoTask.setConcatResultStr(String.format("\n调规则过程发生异常:%s", e.getMessage()));
            callBakMsg = new JSONObject();
        }
        //获取到的返回结果存入中间变量
        autoTask.getTempValues().put("ruleInfo", callBakMsg);
        log.info("规则返回结果，222222222：" + callBakMsg);
        return callBakMsg;
    }


    /**
     * 检查参数是否获取到
     *
     * @param arg
     * @return
     */
    public static String checkParam(String arg) {
        return StringUtil.isEmpty(arg) ? "" : arg;
    }

    /**
     * 从map中检查并获取对应value
     *
     * @param argMap
     * @param key
     * @return
     */
    public static String checkValue(Map argMap, String key) {
        String result = "";
        if (argMap != null && argMap.containsKey(key)) {

            if (argMap.get(key) instanceof List) {
                result = JSONArray.toJSONString(argMap.get(key));
            } else {
                result = String.valueOf(argMap.get(key));
            }

        }
        return result;
    }

    /**
     * 投保类型数据转换
     *
     * @param type
     * @return
     */
    private static String changeFirstType(String type) {
        String result = "";
        switch (type) {
            case "非首次投保":
                result = "0";
                break;
            case "新车首次投保":
                result = "1";
                break;
            case "旧车首次投保":
                result = "2";
                break;
        }
        return result;
    }

    /**
     * 无赔优不浮动原因数据转换
     *
     * @param str
     * @return
     */
    private static String changeReasons(String str) {
        String result = "";
        if (str.contains("没有")) {
            result = "1";
        } else if (str.contains("脱保") && str.contains("以上")) {
            result = "2";
        } else if (str.contains("脱保")) {
            result = "3";
        } else if (str.contains("批改") && str.contains("过户")) {
            result = "4";
        } else if (str.contains("过户车")) {
            result = "5";
        } else if (str.contains("短期")) {
            result = "6";
        }
        /*switch (str) {
            case "没有上年度保单":
                result = "1";
                break;
            case "脱保6个月以上":
                result = "2";
                break;
            case "脱保3-6个月":
                result = "3";
                break;
            case "上张保单做过批改过户":
                result = "4";
                break;
            case "过户车":
                result = "5";
                break;
            case "上张保单为短期单":
                result = "6";
                break;
        }*/
        return result;
    }

    /**
     * 车辆使用性质转换
     *
     * @param str
     * @return
     */
    private static String changeUseProps(String str) {
        String result;
        switch (str) {
            case "2"://出租租赁营业客车
                result = "3";
                break;
            case "3"://城市公交营业客车
                result = "4";
                break;
            case "4"://公路客运营业客车
                result = "5";
                break;
            case "6"://营业货车
                result = "7";
                break;
            case "10"://企业非营业客车
                result = "220";
                break;
            case "11"://机关非营业客车
                result = "230";
                break;
            case "12"://非营业货车
                result = "240";
                break;
            case "15"://营业特种车
                result = "301";
                break;
            case "16"://非营业特种车
                result = "302";
                break;
            default:
                result = str;
        }
        return result;
    }

    /**
     * 所属性质转换
     *
     * @param str
     * @return
     */
    private static String changeBelongsNatureCode(String str) {
        String result = "";
        switch (str) {
            case "0"://个人用车
                result = "1";
                break;
            case "1"://企业用车
                result = "2";
                break;
            case "2"://机关用车
                result = "3";
                break;
        }
        return result;
    }





    /**
     * 上年商业理赔次数数据转换
     *
     * @param str
     * @return
     */
    private static String commercialClaimTimes(String str) {
        String result = "";
        switch (str) {
            case "连续承保期间没有出险":
                result = "0";
                break;
            case "连续承保期间出险一次":
                result = "1";
                break;
            case "连续承保期间出险两次":
                result = "2";
                break;
            case "连续承保期间出险三次":
                result = "3";
                break;
            case "连续承保期间出险四次":
                result = "4";
                break;
            case "连续承保期间出险五次":
                result = "5";
                break;
            case "连续承保期间出险六次":
                result = "6";
                break;
            case "连续承保期间出险七次":
                result = "7";
                break;
            case "连续承保期间出险八次":
                result = "8";
                break;
            case "连续承保期间出险九次":
                result = "9";
                break;
            case "连续承保期间出险十次":
                result = "10";
                break;
            case "连续承保期间出险十次以上":
                result = "11";
                break;

        }
        return result;
    }


    /**
     * 上年交强理赔次数数据转换
     *
     * @param str
     * @return
     */
    private static String compulsoryClaimTimes(String str) {
        String result = "";
        switch (str) {
            case "连续三年没有理赔":
                result = "-2";
                break;
            case "连续两年没有理赔":
                result = "-1";
                break;
            case "上年没有理赔":
                result = "0";
                break;
            case "新保或上年发生一次有责任不涉及死亡理赔":
                result = "1";
                break;
            case "上年有两次及以上理赔":
                result = "2";
                break;
            case "上年有涉及死亡理赔":
                result = "3";
                break;
        }
        return result;
    }

    /**
     * 特种车类型数据转换
     *
     * @param str
     * @return
     */
    private static String changeSpecialType(String str) {
        String result = "";
        switch (str) {
            case "ZA"://特种车一
                result = "1";
                break;
            case "ZB"://特种车二
                result = "2";
                break;
            case "ZC"://特种车三
                result = "3";
                break;
            case "ZD"://特种车四
                result = "4";
                break;
        }
        return result;
    }

    /**
     * @param enquiry
     * @param tempValues
     * @param comCode    保险公司出单系统账号
     * @param agentCode  代理点编号
     * @param affCode    业务归属代码
     */
    public static void doRuleInfo(AutoTask autoTask, Map<String, Object> enquiry, Map<String, Object> tempValues, String comCode, String agentCode, String affCode) {
        Enquiry transform = TaskUtil.transform(JSON.toJSONString(enquiry), autoTask.getTaskType());
        transform.getOrder().setInsurePerson(JSON.parseObject(enquiry.get("insurePerson").toString(), InsurePerson.class));
        String insuredPersonListStr = enquiry.get("insuredPersonList").toString();
        List<InsurePerson> insuredPersonList = JSON.parseArray(insuredPersonListStr, InsurePerson.class);
        transform.getOrder().setInsuredPersons(insuredPersonList);
        autoTask.setTempValues(tempValues);
        Object platformInfo = tempValues.get("platformInfo");
        if (null == platformInfo) {
            autoTask.setTaskEntity(transform);
            PlatformUtil.initPlatformInfo(autoTask);
        } else {
            transform.getOrder().setPlatformInfo((Map<String, Object>) platformInfo);
            autoTask.setTaskEntity(transform);
        }

        doRuleInfo(autoTask, comCode, agentCode, affCode);
        tempValues.put("ruleInfo", autoTask.getTempValues().get("ruleInfo"));

        Map<String, Object> taskEntity = new HashMap<>();
        taskEntity.put("enquiry", enquiry);
        autoTask.setTaskEntity(taskEntity);
    }

}
